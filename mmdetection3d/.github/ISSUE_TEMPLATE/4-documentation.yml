name: 📚 Documentation
description: Report an issue related to the documentation.
labels: "kind/doc,status/unconfirmed"
title: "[Docs] "

body:
- type: dropdown
  id: branch
  attributes:
    label: Branch
    description: This issue is related to the
    options:
      - master branch  https://mmdetection3d.readthedocs.io/en/latest/
      - 1.1x branch https://mmdetection3d.readthedocs.io/en/1.1/
  validations:
    required: true

- type: textarea
  attributes:
    label: 📚 The doc issue
    description: >
      A clear and concise description the issue.
  validations:
    required: true

- type: textarea
  attributes:
    label: Suggest a potential alternative/fix
    description: >
      Tell us how we could improve the documentation in this regard.
- type: markdown
  attributes:
    value: >
      Thanks for contributing 🎉!
