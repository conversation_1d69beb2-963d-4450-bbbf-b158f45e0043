name: lint

on: [push, pull_request]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    runs-on: ubuntu-18.04
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python 3.7
        uses: actions/setup-python@v1
        with:
          python-version: 3.7
      - name: Install linting dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8==5.0.4 isort==5.10.1 yapf==v0.32.0 interrogate
      - name: Lint with flake8
        run: flake8 .
      - name: Lint with isort
        run: isort --recursive --check-only --diff mmdet3d/ tests/ examples/
      - name: Format python codes with yapf
        run: yapf -r -d mmdet3d/ tests/ examples/
      - name: Check docstring
        run: interrogate -v --ignore-init-method --ignore-module --ignore-nested-functions --exclude mmdet3d/ops --ignore-regex "__repr__" --fail-under 95 mmdet3d
      - name: Check doc link
        run: |
          python3 .github/scripts/doc_link_checker.py --target docs/zh_cn
          python3 .github/scripts/doc_link_checker.py --target README_zh-CN.md
          python3 .github/scripts/doc_link_checker.py --target docs/en
          python3 .github/scripts/doc_link_checker.py --target README.md
