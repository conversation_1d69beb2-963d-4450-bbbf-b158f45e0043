Collections:
  - Name: ImVoteNet
    Metadata:
      Training Data: SUNRGBD
      Training Techniques:
        - AdamW
      Training Resources: 8x TITAN Xp
      Architecture:
        - Faster R-CNN
        - VoteNet
        - Feature Pyramid Network
    Paper:
      URL: https://arxiv.org/abs/2001.10692
      Title: 'ImVoteNet: Boosting 3D Object Detection in Point Clouds with Image Votes'
    README: configs/imvotenet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection3d/blob/master/mmdet3d/models/detectors/imvotenet.py#L56
      Version: v0.12.0

Models:
  - Name: imvotenet_faster_rcnn_r50_fpn_2x4_sunrgbd-3d-10class
    In Collection: ImVoteNet
    Config: configs/imvotenet/imvotenet_faster_rcnn_r50_fpn_2x4_sunrgbd-3d-10class.py
    Metadata:
      Training Memory (GB): 2.1
    Results:
      - Task: Object Detection
        Dataset: SUNRGBD-2D
        Metrics:
          AP@0.5: 62.70
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/imvotenet/imvotenet_faster_rcnn_r50_fpn_2x4_sunrgbd-3d-10class/imvotenet_faster_rcnn_r50_fpn_2x4_sunrgbd-3d-10class_20210819_225618-62eba6ce.pth

  - Name: imvotenet_stage2_16x8_sunrgbd-3d-10class
    In Collection: ImVoteNet
    Config: configs/imvotenet/imvotenet_stage2_16x8_sunrgbd-3d-10class.py
    Metadata:
      Training Memory (GB): 9.4
    Results:
      - Task: 3D Object Detection
        Dataset: SUNRGBD-3D
        Metrics:
          AP@0.25: 64.55
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/imvotenet/imvotenet_stage2_16x8_sunrgbd-3d-10class/imvotenet_stage2_16x8_sunrgbd-3d-10class_20210819_192851-1bcd1b97.pth
