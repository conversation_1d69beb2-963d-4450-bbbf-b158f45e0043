Collections:
  - Name: H3DNet
    Metadata:
      Training Data: ScanNet
      Training Techniques:
        - AdamW
      Training Resources: 8x GeForce GTX 1080 Ti
      Architecture:
    Paper:
      URL: https://arxiv.org/abs/2006.05682
      Title: 'H3DNet: 3D Object Detection Using Hybrid Geometric Primitives'
    README: configs/h3dnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection3d/blob/master/mmdet3d/models/detectors/h3dnet.py#L10
      Version: v0.6.0

Models:
  - Name: h3dnet_3x8_scannet-3d-18class
    In Collection: H3DNet
    Config: configs/h3dnet/h3dnet_3x8_scannet-3d-18class.py
    Metadata:
      Training Memory (GB): 7.9
    Results:
      - Task: 3D Object Detection
        Dataset: ScanNet
        Metrics:
          AP@0.25: 66.07
          AP@0.5: 47.68
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/h3dnet/h3dnet_3x8_scannet-3d-18class/h3dnet_3x8_scannet-3d-18class_20210824_003149-414bd304.pth
