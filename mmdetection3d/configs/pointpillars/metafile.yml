Collections:
  - Name: PointPillars
    Metadata:
      Training Techniques:
        - AdamW
      Architecture:
        - Feature Pyramid Network
    Paper:
      URL: https://arxiv.org/abs/1812.05784
      Title: 'PointPillars: Fast Encoders for Object Detection from Point Clouds'
    README: configs/pointpillars/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection3d/blob/master/mmdet3d/models/voxel_encoders/pillar_encoder.py#L13
      Version: v0.6.0

Models:
  - Name: hv_pointpillars_secfpn_6x8_160e_kitti-3d-car
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_6x8_160e_kitti-3d-car.py
    Metadata:
      Training Data: KITTI
      Training Memory (GB): 5.4
      Training Resources: 8x V100 GPUs
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          AP: 77.6
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/pointpillars/hv_pointpillars_secfpn_6x8_160e_kitti-3d-car/hv_pointpillars_secfpn_6x8_160e_kitti-3d-car_20220331_134606-d42d15ed.pth

  - Name: hv_pointpillars_secfpn_6x8_160e_kitti-3d-3class
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_6x8_160e_kitti-3d-3class.py
    Metadata:
      Training Data: KITTI
      Training Memory (GB): 5.5
      Training Resources: 8x V100 GPUs
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          AP: 64.07
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/pointpillars/hv_pointpillars_secfpn_6x8_160e_kitti-3d-3class/hv_pointpillars_secfpn_6x8_160e_kitti-3d-3class_20220301_150306-37dc2420.pth

  - Name: hv_pointpillars_secfpn_sbn-all_4x8_2x_nus-3d
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_sbn-all_4x8_2x_nus-3d.py
    Metadata:
      Training Data: nuScenes
      Training Memory (GB): 16.4
      Training Resources: 8x V100 GPUs
    Results:
      - Task: 3D Object Detection
        Dataset: nuScenes
        Metrics:
          mAP: 34.33
          NDS: 49.1
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/pointpillars/hv_pointpillars_secfpn_sbn-all_4x8_2x_nus-3d/hv_pointpillars_secfpn_sbn-all_4x8_2x_nus-3d_20210826_225857-f19d00a3.pth

  - Name: hv_pointpillars_fpn_sbn-all_4x8_2x_nus-3d
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_fpn_sbn-all_4x8_2x_nus-3d.py
    Metadata:
      Training Data: nuScenes
      Training Memory (GB): 16.3
      Training Resources: 8x V100 GPUs
    Results:
      - Task: 3D Object Detection
        Dataset: nuScenes
        Metrics:
          mAP: 39.71
          NDS: 53.15
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/pointpillars/hv_pointpillars_fpn_sbn-all_4x8_2x_nus-3d/hv_pointpillars_fpn_sbn-all_4x8_2x_nus-3d_20210826_104936-fca299c1.pth

  - Name: hv_pointpillars_secfpn_sbn-all_2x8_2x_lyft-3d
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_sbn-all_2x8_2x_lyft-3d.py
    Metadata:
      Training Data: Lyft
      Training Memory (GB): 12.2
      Training Resources: 8x V100 GPUs
    Results:
      - Task: 3D Object Detection
        Dataset: Lyft
        Metrics:
          Private Score: 13.8
          Public Score: 14.1
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/pointpillars/hv_pointpillars_secfpn_sbn-all_2x8_2x_lyft-3d/hv_pointpillars_secfpn_sbn-all_2x8_2x_lyft-3d_20210829_100455-82b81c39.pth

  - Name: hv_pointpillars_fpn_sbn-all_2x8_2x_lyft-3d
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_fpn_sbn-all_2x8_2x_lyft-3d.py
    Metadata:
      Training Data: Lyft
      Training Memory (GB): 9.2
      Training Resources: 8x V100 GPUs
    Results:
      - Task: 3D Object Detection
        Dataset: Lyft
        Metrics:
          Private Score: 14.0
          Public Score: 15.0
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/pointpillars/hv_pointpillars_fpn_sbn-all_2x8_2x_lyft-3d/hv_pointpillars_fpn_sbn-all_2x8_2x_lyft-3d_20210822_095429-0b3d6196.pth

  - Name: hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-car
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-car.py
    Metadata:
      Training Data: Waymo
      Training Memory (GB): 7.76
      Training Resources: 8x GeForce GTX 1080 Ti
    Results:
      - Task: 3D Object Detection
        Dataset: Waymo
        Metrics:
          mAP@L1: 70.2
          mAPH@L1: 69.6
          mAP@L2: 62.6
          mAPH@L2: 62.1
    Weights: https://download.openmmlab.com/mmdetection3d/v0.1.0_models/pointpillars/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-car/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-car_20200901_204315-302fc3e7.pth

  - Name: hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-3class
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-3class.py
    Metadata:
      Training Data: Waymo
      Training Memory (GB): 8.12
      Training Resources: 8x GeForce GTX 1080 Ti
    Results:
      - Task: 3D Object Detection
        Dataset: Waymo
        Metrics:
          mAP@L1: 64.7
          mAPH@L1: 57.6
          mAP@L2: 58.4
          mAPH@L2: 52.1
    Weights: https://download.openmmlab.com/mmdetection3d/v0.1.0_models/pointpillars/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-3class/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-3class_20200831_204144-d1a706b1.pth

  - Name: hv_pointpillars_secfpn_sbn_2x16_2x_waymo-3d-car
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_sbn_2x16_2x_waymo-3d-car.py
    Metadata:
      Training Data: Waymo
      Training Memory (GB): 7.76
      Training Resources: 8x GeForce GTX 1080 Ti
    Results:
      - Task: 3D Object Detection
        Dataset: Waymo
        Metrics:
          mAP@L1: 72.1
          mAPH@L1: 71.5
          mAP@L2: 63.6
          mAPH@L2: 63.1
    Weights: https://download.openmmlab.com/mmdetection3d/v0.1.0_models/pointpillars/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-car/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-car_20200901_204315-302fc3e7.pth

  - Name: hv_pointpillars_secfpn_sbn_2x16_2x_waymo-3d-3class
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_sbn_2x16_2x_waymo-3d-3class.py
    Metadata:
      Training Data: Waymo
      Training Memory (GB): 8.12
      Training Resources: 8x GeForce GTX 1080 Ti
    Results:
      - Task: 3D Object Detection
        Dataset: Waymo
        Metrics:
          mAP@L1: 68.8
          mAPH@L1: 63.3
          mAP@L2: 62.6
          mAPH@L2: 57.6
    Weights: https://download.openmmlab.com/mmdetection3d/v0.1.0_models/pointpillars/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-3class/hv_pointpillars_secfpn_sbn_2x16_2x_waymoD5-3d-3class_20200831_204144-d1a706b1.pth

  - Name: hv_pointpillars_secfpn_sbn-all_fp16_2x8_2x_nus-3d
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_secfpn_sbn-all_fp16_2x8_2x_nus-3d.py
    Metadata:
      Training Techniques:
        - AdamW
        - Mixed Precision Training
      Training Resources: 8x TITAN Xp
      Architecture:
        - Hard Voxelization
      Training Data: nuScenes
      Training Memory (GB): 8.37
    Results:
      - Task: 3D Object Detection
        Dataset: nuScenes
        Metrics:
          mAP: 35.19
          NDS: 50.27
    Weights: https://download.openmmlab.com/mmdetection3d/v0.1.0_models/fp16/hv_pointpillars_secfpn_sbn-all_fp16_2x8_2x_nus-3d/hv_pointpillars_secfpn_sbn-all_fp16_2x8_2x_nus-3d_20201020_222626-c3f0483e.pth
    Code:
      Version: v0.7.0

  - Name: hv_pointpillars_fpn_sbn-all_fp16_2x8_2x_nus-3d
    In Collection: PointPillars
    Config: configs/pointpillars/hv_pointpillars_fpn_sbn-all_fp16_2x8_2x_nus-3d.py
    Metadata:
      Training Techniques:
        - AdamW
        - Mixed Precision Training
      Training Resources: 8x TITAN Xp
      Architecture:
        - Hard Voxelization
      Training Data: nuScenes
      Training Memory (GB): 8.40
    Results:
      - Task: 3D Object Detection
        Dataset: nuScenes
        Metrics:
          mAP: 39.26
          NDS: 53.26
    Weights: https://download.openmmlab.com/mmdetection3d/v0.1.0_models/fp16/hv_pointpillars_fpn_sbn-all_fp16_2x8_2x_nus-3d/hv_pointpillars_fpn_sbn-all_fp16_2x8_2x_nus-3d_20201021_120719-269f9dd6.pth
    Code:
      Version: v0.7.0
