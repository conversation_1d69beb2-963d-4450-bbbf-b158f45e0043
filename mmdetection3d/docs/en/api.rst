mmdet3d.core
--------------

anchor
^^^^^^^^^^
.. automodule:: mmdet3d.core.anchor
    :members:

bbox
^^^^^^^^^^
.. automodule:: mmdet3d.core.bbox
    :members:

evaluation
^^^^^^^^^^
.. automodule:: mmdet3d.core.evaluation
    :members:

visualizer
^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.core.visualizer
    :members:

voxel
^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.core.voxel
    :members:

post_processing
^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.core.post_processing
    :members:

mmdet3d.datasets
----------------

.. automodule:: mmdet3d.datasets
    :members:

mmdet3d.models
--------------

detectors
^^^^^^^^^^
.. automodule:: mmdet3d.models.detectors
    :members:

backbones
^^^^^^^^^^
.. automodule:: mmdet3d.models.backbones
    :members:

necks
^^^^^^^^^^
.. automodule:: mmdet3d.models.necks
    :members:

dense_heads
^^^^^^^^^^^^
.. automodule:: mmdet3d.models.dense_heads
    :members:

roi_heads
^^^^^^^^^^
.. automodule:: mmdet3d.models.roi_heads
    :members:

fusion_layers
^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.fusion_layers
    :members:

losses
^^^^^^^^^^
.. automodule:: mmdet3d.models.losses
    :members:

middle_encoders
^^^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.middle_encoders
    :members:

model_utils
^^^^^^^^^^^^^
.. automodule:: mmdet3d.models.model_utils
    :members:
