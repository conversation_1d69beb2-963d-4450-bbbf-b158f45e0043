import os
import glob
import pickle
import numpy as np
import json
import random
try:
    from tqdm import tqdm
except ImportError:
    def tqdm(x):
        return x
from multiprocessing.pool import Pool
import cv2
try:
    import open3d as o3d
    OPEN3D_AVAILABLE = True
except ImportError:
    OPEN3D_AVAILABLE = False
    print("Warning: Open3D not available. 3D visualization will be disabled.")

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL not available. Some image operations may be limited.")

label_list = ['pedestrian','Cyclist_non','Cyclist_has','tricycle','car','bus','van','truck','trafficcone','other_vehicle', 'temponary_light']
root_path = '/mnt/0618data/welabel_dataset'

#   BSC - 短距，补盲
#   VC - 普通，针孔
#   FC - front center
#   第三个字母   M：中距离 N：普通 W：广角
# 相机名称列表
CAMERA = [
    "FCMVC_front",
    "FCNVC_front",
    "FCWVC_front",
    "BCWVC_back",
    "NLMVC_front_left",
    "NLWVC_back_left",
    "NRMVC_front_right",
    "NRWVC_back_right"
    "NCBSC_front",
    "NLBSC_left",
    "BRBSC_back",
    "NRBSC_right",
]

LIDAR = [
    "merged_pcd",
]

# Wenyuan类别到目标类别的映射
Wen2HvCatMapping = {
    'Car': 'car',
    'Pedestrian': 'pedestrian',
    'Cyclist': 'Cyclist_has',
    'Truck': 'truck',
    'Bus': 'bus',
    'Van': 'van',
    'Associated_Obstacle': 'other_vehicle',
    'curb': 'other_vehicle'
}

class Wenyuan2HV:
    def __init__(self, root_path, label_list, with_camera = True, with_lidar = True, max_sweeps=1):
        self.root_path = root_path
        self.label_list = label_list
        self.max_sweeps = max_sweeps
        self.with_camera = with_camera
        self.with_lidar = with_lidar
        self.clips = []
        self.samples = []
        self._initFilePath()

    def _loadJson(self, json_path):
        with open(json_path, 'r') as file:
            data = json.load(file)
        return data

    def _initFilePath(self):
        """初始化文件路径，扫描所有clips和frames"""
        print(f"Scanning data from {self.root_path}")

        # 获取所有clip目录
        clip_dirs = [d for d in os.listdir(self.root_path)
                    if os.path.isdir(os.path.join(self.root_path, d)) and d.startswith('welabel_lidar')]

        for clip_dir in clip_dirs:
            clip_path = os.path.join(self.root_path, clip_dir)
            frames_path = os.path.join(clip_path, 'frames')

            if not os.path.exists(frames_path):
                continue

            # 获取该clip下的所有frame
            frame_dirs = [f for f in os.listdir(frames_path)
                         if os.path.isdir(os.path.join(frames_path, f))]

            clip_info = {
                'clip_name': clip_dir,
                'clip_path': clip_path,
                'frames_path': frames_path,
                'frame_dirs': sorted(frame_dirs)  # 按时间戳排序
            }
            self.clips.append(clip_info)

            # 为每个frame创建sample信息
            for frame_dir in frame_dirs:
                frame_path = os.path.join(frames_path, frame_dir)

                # 检查必要文件是否存在
                pcd_path = os.path.join(frame_path, 'merged_pcd.pcd')
                gt_path = os.path.join(frame_path, 'ground_truth', 'lidar_detect', 'merged_pcd.json')
                localization_path = os.path.join(frame_path, 'localization-current_pose.json')
                lidar_imu_path = os.path.join(frame_path, 'lidar_imu-merged_pcd.json')

                if not all([os.path.exists(pcd_path), os.path.exists(gt_path),
                           os.path.exists(localization_path), os.path.exists(lidar_imu_path)]):
                    continue

                sample_info = {
                    'clip_name': clip_dir,
                    'frame_name': frame_dir,
                    'frame_path': frame_path,
                    'timestamp': frame_dir,  # frame目录名就是时间戳
                    'pcd_path': pcd_path,
                    'gt_path': gt_path,
                    'localization_path': localization_path,
                    'lidar_imu_path': lidar_imu_path,
                    'camera_paths': {}
                }

                # 添加相机文件路径
                if self.with_camera:
                    for cam_name in CAMERA:
                        cam_img_path = os.path.join(frame_path, f'{cam_name}.webp')
                        cam_calib_path = os.path.join(frame_path, f'camera_calibration-{cam_name}.json')

                        if os.path.exists(cam_img_path) and os.path.exists(cam_calib_path):
                            sample_info['camera_paths'][cam_name] = {
                                'img_path': cam_img_path,
                                'calib_path': cam_calib_path
                            }

                self.samples.append(sample_info)

        print(f"Found {len(self.clips)} clips with {len(self.samples)} samples")

    def getTrainValClips(self, train_ratio=0.9):
        """按比例分割训练集和验证集"""
        # 按clip进行分割，确保同一个clip的所有frames都在同一个集合中
        clip_names = [clip['clip_name'] for clip in self.clips]
        random.shuffle(clip_names)

        split_idx = int(len(clip_names) * train_ratio)
        train_clip_names = set(clip_names[:split_idx])
        val_clip_names = set(clip_names[split_idx:])

        train_samples = [sample for sample in self.samples
                        if sample['clip_name'] in train_clip_names]
        val_samples = [sample for sample in self.samples
                      if sample['clip_name'] in val_clip_names]

        print(f"Train clips: {len(train_clip_names)}, samples: {len(train_samples)}")
        print(f"Val clips: {len(val_clip_names)}, samples: {len(val_samples)}")

        return train_samples, val_samples

    def _quaternion_to_rotation_matrix(self, quat):
        """四元数转旋转矩阵"""
        x, y, z, w = quat['x'], quat['y'], quat['z'], quat['w']

        # 归一化四元数
        norm = np.sqrt(x*x + y*y + z*z + w*w)
        x, y, z, w = x/norm, y/norm, z/norm, w/norm

        # 转换为旋转矩阵
        rotation_matrix = np.array([
            [1 - 2*(y*y + z*z), 2*(x*y - z*w), 2*(x*z + y*w)],
            [2*(x*y + z*w), 1 - 2*(x*x + z*z), 2*(y*z - x*w)],
            [2*(x*z - y*w), 2*(y*z + x*w), 1 - 2*(x*x + y*y)]
        ])
        return rotation_matrix

    def _rodrigues_to_rotation_matrix(self, rvec):
        """罗德里格斯向量转旋转矩阵"""
        theta = np.linalg.norm(rvec)
        if theta < 1e-6:
            return np.eye(3)

        k = rvec / theta
        K = np.array([
            [0, -k[2], k[1]],
            [k[2], 0, -k[0]],
            [-k[1], k[0], 0]
        ])

        R = np.eye(3) + np.sin(theta) * K + (1 - np.cos(theta)) * np.dot(K, K)
        return R

    def _parse_camera_calibration(self, calib_path):
        """解析相机标定文件"""
        calib_data = self._loadJson(calib_path)

        # 相机内参
        cam_intrinsic = np.array(calib_data['camera_matrix'])

        # 相机外参 (相机到车体的变换)
        tvec = np.array(calib_data['tvec'])
        rvec = np.array(calib_data['rvec'])

        # 转换为旋转矩阵
        rotation_matrix = self._rodrigues_to_rotation_matrix(rvec)

        # 构建4x4变换矩阵 (camera to ego)
        cam2ego = np.eye(4)
        cam2ego[:3, :3] = rotation_matrix
        cam2ego[:3, 3] = tvec

        return {
            'cam_intrinsic': cam_intrinsic,
            'cam2ego_rotation': rotation_matrix.flatten().tolist(),
            'cam2ego_translation': tvec.tolist(),
            'cam2ego_matrix': cam2ego
        }

    def _parse_3d_boxes(self, gt_path):
        """解析3D标注框"""
        gt_data = self._loadJson(gt_path)

        gt_boxes = []
        gt_names = []
        track_ids = []

        # 检查是否有3d_box数据
        if 'ground_truth' not in gt_data or 'ground_truth' not in gt_data['ground_truth']:
            return gt_boxes, gt_names, track_ids

        gt_info = gt_data['ground_truth']['ground_truth']['data']
        if '3d_box' not in gt_info:
            return gt_boxes, gt_names, track_ids

        boxes_data = gt_info['3d_box']['labels']

        for box_data in boxes_data:
            # 获取类别
            category = box_data.get('category', 'Unknown')
            if category not in Wen2HvCatMapping:
                continue  # 跳过未知类别

            mapped_category = Wen2HvCatMapping[category]
            if mapped_category not in self.label_list:
                continue  # 跳过不在目标类别列表中的类别

            # 获取中心点坐标
            center = box_data['center']
            x, y, z = center['x'], center['y'], center['z']

            # 获取尺寸 (长宽高)
            size = box_data['size']
            l, w, h = size['x'], size['y'], size['z']

            # 获取旋转角度 (yaw)
            yaw = box_data['yaw']

            # 获取track_id
            track_id = box_data.get('track_id', -1)

            # 构建box格式: [x, y, z, l, w, h, yaw]
            box = [x, y, z, l, w, h, yaw]

            gt_boxes.append(box)
            gt_names.append(mapped_category)
            track_ids.append(track_id)

        return np.array(gt_boxes), gt_names, track_ids

    def _parse_pose_info(self, localization_path, lidar_imu_path):
        """解析位置和姿态信息"""
        # 解析全局位置信息
        localization_data = self._loadJson(localization_path)
        ego_position = localization_data['position']
        ego_orientation = localization_data['orientation']

        # 解析lidar到ego的变换
        lidar_imu_data = self._loadJson(lidar_imu_path)
        lidar_position = lidar_imu_data['position']
        lidar_orientation = lidar_imu_data['orientation']

        # ego到global的变换
        ego2global_translation = [ego_position['x'], ego_position['y'], ego_position['z']]
        ego2global_rotation = self._quaternion_to_rotation_matrix(ego_orientation)

        # lidar到ego的变换
        lidar2ego_translation = [lidar_position['x'], lidar_position['y'], lidar_position['z']]
        lidar2ego_rotation = self._quaternion_to_rotation_matrix(lidar_orientation)

        return {
            'ego2global_translation': ego2global_translation,
            'ego2global_rotation': ego2global_rotation.flatten().tolist(),
            'lidar2ego_translation': lidar2ego_translation,
            'lidar2ego_rotation': lidar2ego_rotation.flatten().tolist()
        }

    def createInfos(self, samples):
        infos = []
        print(f"Processing {len(samples)} samples...")

        for i, sample in enumerate(tqdm(samples)):
            try:
                # 解析3D标注框
                gt_boxes, gt_names, track_ids = self._parse_3d_boxes(sample['gt_path'])

                # 解析位置和姿态信息
                pose_info = self._parse_pose_info(sample['localization_path'], sample['lidar_imu_path'])

                # 处理相机信息
                cams_info = {}
                cams_seq = []

                if self.with_camera:
                    for cam_name, cam_paths in sample['camera_paths'].items():
                        try:
                            cam_calib = self._parse_camera_calibration(cam_paths['calib_path'])

                            cam_info = {
                                'data_path': cam_paths['img_path'],
                                'type': cam_name,
                                'sample_data_token': f"{sample['timestamp']}_{cam_name}",
                                'sensor2ego_translation': cam_calib['cam2ego_translation'],
                                'sensor2ego_rotation': cam_calib['cam2ego_rotation'],
                                'ego2global_translation': pose_info['ego2global_translation'],
                                'ego2global_rotation': pose_info['ego2global_rotation'],
                                'timestamp': int(sample['timestamp']),
                                'cam_intrinsic': cam_calib['cam_intrinsic'].tolist(),
                            }
                            cams_info[cam_name] = cam_info
                            cams_seq.append(cam_name)
                        except Exception as e:
                            print(f"Warning: Failed to process camera {cam_name} for sample {sample['timestamp']}: {e}")
                            continue

                # 构建info字典
                info = {
                    "lidar_path": sample['pcd_path'],
                    "token": sample['timestamp'],
                    "sweeps": [],  # 暂时不处理sweeps
                    "cams_seq": cams_seq,
                    "cams_info": cams_info,
                    "lidar2ego_translation": pose_info['lidar2ego_translation'],
                    "lidar2ego_rotation": pose_info['lidar2ego_rotation'],
                    "ego2global_translation": pose_info['ego2global_translation'],
                    "ego2global_rotation": pose_info['ego2global_rotation'],
                    "timestamp": int(sample['timestamp']),
                    "gt_boxes": gt_boxes.tolist() if len(gt_boxes) > 0 else [],
                    "gt_names": gt_names,
                    "gt_boxes_2d": [],  # 暂时不处理2D框
                    "gt_names_2d": [],
                    "gt_rotations": [],
                    "gt_velocity": [],  # 暂时不处理速度
                    "track_ids": track_ids,
                    "track_gt_centers": [],
                    "center_in_global": [],
                    "num_lidar_pts": [],  # 暂时不计算点数
                    "num_radar_pts": [],
                    "valid_flag": [True] * len(gt_boxes) if len(gt_boxes) > 0 else [],
                    "scene_token": sample['clip_name'],
                }
                infos.append(info)

            except Exception as e:
                print(f"Error processing sample {sample['timestamp']}: {e}")
                continue

        print(f"Successfully processed {len(infos)} samples")
        return infos

    def create(self):
        train_samples, val_samples = self.getTrainValClips()
        train_infos = self.createInfos(train_samples)
        val_infos = self.createInfos(val_samples)
        return train_infos, val_infos

    def save(self, train_infos, val_infos, save_path=None):
        """保存转换后的数据为pickle文件"""
        if save_path is None:
            save_path = self.root_path

        train_file = os.path.join(save_path, 'wenyuan_infos_train.pkl')
        val_file = os.path.join(save_path, 'wenyuan_infos_val.pkl')

        print(f"Saving train infos to {train_file}")
        with open(train_file, 'wb') as f:
            pickle.dump(train_infos, f)

        print(f"Saving val infos to {val_file}")
        with open(val_file, 'wb') as f:
            pickle.dump(val_infos, f)

        print(f"Saved {len(train_infos)} train samples and {len(val_infos)} val samples")

    def _get_3d_box_corners(self, box):
        """获取3D框的8个角点坐标"""
        x, y, z, l, w, h, yaw = box

        # 在局部坐标系中定义8个角点 (车体坐标系，x前y左z上)
        corners = np.array([
            [-l/2, -w/2, -h/2],  # 0: 后右下
            [ l/2, -w/2, -h/2],  # 1: 前右下
            [ l/2,  w/2, -h/2],  # 2: 前左下
            [-l/2,  w/2, -h/2],  # 3: 后左下
            [-l/2, -w/2,  h/2],  # 4: 后右上
            [ l/2, -w/2,  h/2],  # 5: 前右上
            [ l/2,  w/2,  h/2],  # 6: 前左上
            [-l/2,  w/2,  h/2],  # 7: 后左上
        ])

        # 旋转矩阵 (绕z轴旋转)
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        rotation_matrix = np.array([
            [cos_yaw, -sin_yaw, 0],
            [sin_yaw,  cos_yaw, 0],
            [0,        0,       1]
        ])

        # 旋转并平移
        corners = corners @ rotation_matrix.T
        corners[:, 0] += x
        corners[:, 1] += y
        corners[:, 2] += z

        return corners

    def _project_3d_to_2d(self, points_3d, cam_intrinsic, cam2ego_matrix, ego2global_matrix=None):
        """将3D点投影到2D图像坐标

        Args:
            points_3d: 3D点坐标，假设在ego坐标系中 [N, 3]
            cam_intrinsic: 相机内参矩阵 [3, 3]
            cam2ego_matrix: 相机到ego的变换矩阵 [4, 4]
            ego2global_matrix: ego到全局的变换矩阵 [4, 4]，此处不使用

        Returns:
            points_2d: 2D图像坐标 [N, 2]
            valid_mask: 有效点的掩码 [N]
        """
        # 转换为齐次坐标
        points_3d_homo = np.hstack([points_3d, np.ones((points_3d.shape[0], 1))])

        # ego坐标系 -> 相机坐标系
        # 注意：cam2ego_matrix 是相机到ego的变换，我们需要其逆矩阵
        ego2cam = np.linalg.inv(cam2ego_matrix)

        # 变换到相机坐标系
        points_cam = points_3d_homo @ ego2cam.T

        # 只保留相机前方的点 (z > 0)
        valid_mask = points_cam[:, 2] > 0.1

        if not np.any(valid_mask):
            return None, None

        # 投影到图像平面
        points_cam_3d = points_cam[:, :3]  # 只取xyz坐标
        points_2d_homo = points_cam_3d @ cam_intrinsic.T  # 3x3 矩阵乘法
        points_2d = points_2d_homo[:, :2] / points_2d_homo[:, 2:3]

        return points_2d, valid_mask

    def visualize_boxes_on_images(self, sample_info, save_dir=None):
        """将3D框投影到各个相机图像上并保存

        Args:
            sample_info: 单个样本的info字典
            save_dir: 保存目录，如果为None则不保存
        """
        if not sample_info['gt_boxes']:
            print("No GT boxes to visualize")
            return

        if save_dir:
            os.makedirs(save_dir, exist_ok=True)

        print(f"Visualizing {len(sample_info['gt_boxes'])} boxes on {len(sample_info['cams_info'])} cameras...")

        # 构建ego2global变换矩阵
        ego2global = np.eye(4)
        ego_rot = np.array(sample_info['ego2global_rotation']).reshape(3, 3)
        ego_trans = np.array(sample_info['ego2global_translation'])
        ego2global[:3, :3] = ego_rot
        ego2global[:3, 3] = ego_trans

        for cam_name, cam_info in sample_info['cams_info'].items():
            try:
                # 读取图像
                img_path = cam_info['data_path']
                if not os.path.exists(img_path):
                    print(f"Warning: Image not found: {img_path}")
                    continue

                # 使用cv2读取图像
                img = cv2.imread(img_path)
                if img is None:
                    print(f"Warning: Failed to load image: {img_path}")
                    continue

                # 获取相机参数
                cam_intrinsic = np.array(cam_info['cam_intrinsic'])
                cam_rot = np.array(cam_info['sensor2ego_rotation']).reshape(3, 3)
                cam_trans = np.array(cam_info['sensor2ego_translation'])

                # 构建cam2ego变换矩阵
                cam2ego = np.eye(4)
                cam2ego[:3, :3] = cam_rot
                cam2ego[:3, 3] = cam_trans

                # 为每个3D框绘制投影
                for i, (box, name) in enumerate(zip(sample_info['gt_boxes'], sample_info['gt_names'])):
                    # 获取3D框角点
                    corners_3d = self._get_3d_box_corners(box)

                    # 投影到2D
                    corners_2d, valid_mask = self._project_3d_to_2d(
                        corners_3d, cam_intrinsic, cam2ego
                    )

                    if corners_2d is None:
                        continue

                    # 检查是否在图像范围内
                    h, w = img.shape[:2]
                    in_image = ((corners_2d[:, 0] >= 0) & (corners_2d[:, 0] < w) &
                               (corners_2d[:, 1] >= 0) & (corners_2d[:, 1] < h) & valid_mask)

                    if not np.any(in_image):
                        continue

                    # 绘制3D框
                    corners_2d = corners_2d.astype(int)

                    # 定义框的连接线 (底面4条边 + 顶面4条边 + 4条竖边)
                    edges = [
                        # 底面
                        (0, 1), (1, 2), (2, 3), (3, 0),
                        # 顶面
                        (4, 5), (5, 6), (6, 7), (7, 4),
                        # 竖边
                        (0, 4), (1, 5), (2, 6), (3, 7)
                    ]

                    # 根据类别选择颜色
                    color_map = {
                        'car': (0, 255, 0),           # 绿色
                        'pedestrian': (255, 0, 0),    # 蓝色
                        'Cyclist_has': (0, 255, 255), # 黄色
                        'truck': (255, 0, 255),       # 紫色
                        'bus': (255, 255, 0),         # 青色
                        'van': (128, 0, 128),         # 紫色
                        'other_vehicle': (128, 128, 128)  # 灰色
                    }
                    color = color_map.get(name, (255, 255, 255))  # 默认白色

                    # 绘制边
                    for edge in edges:
                        pt1_idx, pt2_idx = edge
                        if valid_mask[pt1_idx] and valid_mask[pt2_idx]:
                            pt1 = tuple(corners_2d[pt1_idx])
                            pt2 = tuple(corners_2d[pt2_idx])
                            cv2.line(img, pt1, pt2, color, 2)

                    # 绘制类别标签
                    if np.any(in_image):
                        # 找到可见角点的中心作为标签位置
                        visible_corners = corners_2d[valid_mask & in_image]
                        if len(visible_corners) > 0:
                            label_pos = tuple(np.mean(visible_corners, axis=0).astype(int))
                            cv2.putText(img, f"{name}_{i}", label_pos,
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

                # 保存图像
                if save_dir:
                    save_path = os.path.join(save_dir, f"{sample_info['token']}_{cam_name}_boxes.jpg")
                    cv2.imwrite(save_path, img)
                    print(f"Saved: {save_path}")

            except Exception as e:
                print(f"Error processing camera {cam_name}: {e}")
                continue

    def _read_pcd_file(self, pcd_path):
        """读取PCD文件，返回点坐标和反射强度"""
        try:
            with open(pcd_path, 'rb') as f:
                # 跳过文件头
                while True:
                    line = f.readline()
                    if line.startswith(b'DATA'):
                        if b'binary' in line:
                            break
                        else:
                            raise ValueError("Unsupported PCD data type.")

                # 读取二进制数据
                dtypes = [('x', np.float32),
                          ('y', np.float32),
                          ('z', np.float32),
                          ('intensity', np.uint8)]

                point_data = np.fromfile(f, dtype=dtypes)

                # 提取坐标和强度
                points = np.vstack((point_data['x'], point_data['y'], point_data['z'])).T
                intensities = point_data['intensity'].astype(np.float32)

                return points, intensities

        except Exception as e:
            print(f"Error reading PCD file {pcd_path}: {e}")
            return None, None

    def _create_box_mesh(self, box, color=[1, 0, 0]):
        """创建3D框的线框mesh"""
        if not OPEN3D_AVAILABLE:
            return None

        corners = self._get_3d_box_corners(box)

        # 定义12条边的连接
        lines = [
            # 底面
            [0, 1], [1, 2], [2, 3], [3, 0],
            # 顶面
            [4, 5], [5, 6], [6, 7], [7, 4],
            # 竖边
            [0, 4], [1, 5], [2, 6], [3, 7]
        ]

        # 创建线集
        line_set = o3d.geometry.LineSet()
        line_set.points = o3d.utility.Vector3dVector(corners)
        line_set.lines = o3d.utility.Vector2iVector(lines)
        line_set.colors = o3d.utility.Vector3dVector([color for _ in range(len(lines))])

        return line_set

    def _intensity_to_color(self, intensities, colormap='jet'):
        """将反射强度转换为颜色

        Args:
            intensities: 反射强度数组
            colormap: 颜色映射方案 ('jet', 'hot', 'cool', 'viridis')

        Returns:
            colors: RGB颜色数组 [N, 3]
        """
        # 归一化强度值到[0,1]范围
        intensity_min, intensity_max = intensities.min(), intensities.max()
        if intensity_max > intensity_min:
            normalized = (intensities - intensity_min) / (intensity_max - intensity_min)
        else:
            normalized = np.ones_like(intensities) * 0.5

        colors = np.zeros((len(intensities), 3))

        if colormap == 'jet':
            # 蓝->青->绿->黄->红
            for i, val in enumerate(normalized):
                if val < 0.25:
                    t = val / 0.25
                    colors[i] = [0, t, 1]
                elif val < 0.5:
                    t = (val - 0.25) / 0.25
                    colors[i] = [0, 1, 1-t]
                elif val < 0.75:
                    t = (val - 0.5) / 0.25
                    colors[i] = [t, 1, 0]
                else:
                    t = (val - 0.75) / 0.25
                    colors[i] = [1, 1-t, 0]

        elif colormap == 'hot':
            # 黑->红->黄->白
            for i, val in enumerate(normalized):
                if val < 0.33:
                    t = val / 0.33
                    colors[i] = [t, 0, 0]
                elif val < 0.67:
                    t = (val - 0.33) / 0.34
                    colors[i] = [1, t, 0]
                else:
                    t = (val - 0.67) / 0.33
                    colors[i] = [1, 1, t]

        elif colormap == 'cool':
            # 青->紫
            colors[:, 0] = normalized
            colors[:, 1] = 1 - normalized
            colors[:, 2] = 1

        elif colormap == 'viridis':
            # 紫->蓝->绿->黄 (近似)
            for i, val in enumerate(normalized):
                if val < 0.5:
                    t = val / 0.5
                    colors[i] = [0.3*t, 0.2 + 0.6*t, 0.4 + 0.4*t]
                else:
                    t = (val - 0.5) / 0.5
                    colors[i] = [0.3 + 0.7*t, 0.8, 0.8 - 0.8*t]
        else:
            # 默认灰度
            colors[:, 0] = normalized
            colors[:, 1] = normalized
            colors[:, 2] = normalized

        return colors

    def _apply_colormap(self, values, colormap='jet'):
        """将数值映射到颜色

        Args:
            values: 归一化的数值数组 [0, 1]
            colormap: 颜色映射方案 ('jet', 'viridis', 'plasma', 'hot', 'cool')

        Returns:
            colors: RGB颜色数组
        """
        colors = np.zeros((len(values), 3))

        if colormap == 'jet':
            # 经典的蓝->青->绿->黄->红映射
            for i, val in enumerate(values):
                if val < 0.25:
                    # 蓝色到青色
                    t = val / 0.25
                    colors[i] = [0, t, 1]
                elif val < 0.5:
                    # 青色到绿色
                    t = (val - 0.25) / 0.25
                    colors[i] = [0, 1, 1-t]
                elif val < 0.75:
                    # 绿色到黄色
                    t = (val - 0.5) / 0.25
                    colors[i] = [t, 1, 0]
                else:
                    # 黄色到红色
                    t = (val - 0.75) / 0.25
                    colors[i] = [1, 1-t, 0]

        elif colormap == 'viridis':
            # 紫色->蓝色->绿色->黄色
            for i, val in enumerate(values):
                if val < 0.33:
                    t = val / 0.33
                    colors[i] = [0.267*t, 0.004 + 0.314*t, 0.329 + 0.335*t]
                elif val < 0.67:
                    t = (val - 0.33) / 0.34
                    colors[i] = [0.267 + 0.163*t, 0.318 + 0.401*t, 0.664 - 0.218*t]
                else:
                    t = (val - 0.67) / 0.33
                    colors[i] = [0.43 + 0.57*t, 0.719 + 0.281*t, 0.446 - 0.446*t]

        elif colormap == 'plasma':
            # 深紫色->紫色->红色->黄色
            for i, val in enumerate(values):
                if val < 0.33:
                    t = val / 0.33
                    colors[i] = [0.05 + 0.45*t, 0.03 + 0.07*t, 0.53 + 0.17*t]
                elif val < 0.67:
                    t = (val - 0.33) / 0.34
                    colors[i] = [0.5 + 0.4*t, 0.1 + 0.3*t, 0.7 - 0.4*t]
                else:
                    t = (val - 0.67) / 0.33
                    colors[i] = [0.9 + 0.1*t, 0.4 + 0.6*t, 0.3 - 0.3*t]

        elif colormap == 'hot':
            # 黑色->红色->黄色->白色
            for i, val in enumerate(values):
                if val < 0.33:
                    t = val / 0.33
                    colors[i] = [t, 0, 0]
                elif val < 0.67:
                    t = (val - 0.33) / 0.34
                    colors[i] = [1, t, 0]
                else:
                    t = (val - 0.67) / 0.33
                    colors[i] = [1, 1, t]

        elif colormap == 'cool':
            # 青色到紫色
            for i, val in enumerate(values):
                colors[i] = [val, 1-val, 1]

        else:
            # 默认灰度
            for i, val in enumerate(values):
                colors[i] = [val, val, val]

        return colors

    def visualize_pointcloud_with_boxes(self, sample_info, max_points=500000, color_mode='intensity', colormap='jet'):
        """使用Open3D可视化点云和3D框

        Args:
            sample_info: 单个样本的info字典
            max_points: 最大显示点数，用于降采样
            color_mode: 着色模式，'intensity'(反射强度) 或 'height'(高度)
            colormap: 颜色映射方案，'jet', 'hot', 'cool', 'viridis'
        """
        if not OPEN3D_AVAILABLE:
            print("Error: Open3D not available. Please install open3d: pip install open3d")
            return

        if not sample_info['gt_boxes']:
            print("No GT boxes to visualize")
            return

        print(f"Loading point cloud: {sample_info['lidar_path']}")

        # 读取点云和反射强度
        points, intensities = self._read_pcd_file(sample_info['lidar_path'])
        if points is None:
            print("Failed to load point cloud")
            return

        print(f"Loaded {points.shape[0]} points")
        if intensities is not None:
            print(f"Intensity range: {intensities.min():.2f} - {intensities.max():.2f}")
        else:
            print("No intensity information available, using height-based coloring")
            color_mode = 'height'

        # 降采样以提高性能
        if points.shape[0] > max_points:
            indices = np.random.choice(points.shape[0], max_points, replace=False)
            points = points[indices]
            if intensities is not None:
                intensities = intensities[indices]
            print(f"Downsampled to {points.shape[0]} points")

        # 创建点云对象
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)

        # 根据选择的模式给点云着色
        if color_mode == 'intensity' and intensities is not None:
            # 根据反射强度着色
            print(f"Coloring points by intensity using {colormap} colormap...")
            colors = self._intensity_to_color(intensities, colormap)
        else:
            # 根据高度着色 (备用方案)
            print("Coloring points by height...")
            colors = np.zeros_like(points)
            z_min, z_max = points[:, 2].min(), points[:, 2].max()
            if z_max > z_min:
                normalized_z = (points[:, 2] - z_min) / (z_max - z_min)
                colors[:, 0] = normalized_z  # R通道
                colors[:, 1] = 1 - normalized_z  # G通道
                colors[:, 2] = 0.5  # B通道
            else:
                colors[:, :] = [0.5, 0.5, 0.5]  # 灰色

        pcd.colors = o3d.utility.Vector3dVector(colors)

        # 创建可视化对象列表
        vis_objects = [pcd]

        # 为每个3D框创建线框
        color_map = {
            'car': [0, 1, 0],           # 绿色
            'pedestrian': [1, 0, 0],    # 红色
            'Cyclist_has': [1, 1, 0],   # 黄色
            'truck': [1, 0, 1],         # 紫色
            'bus': [0, 1, 1],           # 青色
            'van': [0.5, 0, 0.5],       # 深紫色
            'other_vehicle': [0.5, 0.5, 0.5]  # 灰色
        }

        print(f"Adding {len(sample_info['gt_boxes'])} 3D boxes...")

        for i, (box, name) in enumerate(zip(sample_info['gt_boxes'], sample_info['gt_names'])):
            color = color_map.get(name, [1, 1, 1])  # 默认白色
            box_mesh = self._create_box_mesh(box, color)
            if box_mesh is not None:
                vis_objects.append(box_mesh)

        # 添加坐标系
        coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=5.0, origin=[0, 0, 0])
        vis_objects.append(coord_frame)

        # 可视化
        print("Starting visualization...")
        print("Controls:")
        print("  - Mouse: Rotate view")
        print("  - Mouse wheel: Zoom")
        print("  - Ctrl+Mouse: Pan")
        print("  - Press 'Q' or close window to exit")

        o3d.visualization.draw_geometries(
            vis_objects,
            window_name=f"Point Cloud + 3D Boxes - {sample_info['token']}",
            width=1200,
            height=800,
            left=50,
            top=50
        )

    def debug_visualize_sample(self, sample_index=0, save_images=True, show_3d=True, save_dir="debug_vis", color_mode='intensity', colormap='jet'):
        """调试可视化指定样本

        Args:
            sample_index: 样本索引
            save_images: 是否保存2D投影图像
            show_3d: 是否显示3D可视化
            save_dir: 保存目录
            color_mode: 点云着色模式，'intensity'(反射强度) 或 'height'(高度)
            colormap: 颜色映射方案，'jet', 'hot', 'cool', 'viridis'
        """
        if sample_index >= len(self.samples):
            print(f"Error: Sample index {sample_index} out of range (max: {len(self.samples)-1})")
            return

        print("=" * 60)
        print(f"Debug Visualization - Sample {sample_index}")
        print("=" * 60)

        # 获取样本信息
        sample = self.samples[sample_index]
        print(f"Sample: {sample['timestamp']}")
        print(f"Clip: {sample['clip_name']}")

        # 转换单个样本
        sample_infos = self.createInfos([sample])
        if not sample_infos:
            print("Failed to convert sample")
            return

        sample_info = sample_infos[0]
        print(f"GT boxes: {len(sample_info['gt_boxes'])}")
        print(f"Cameras: {len(sample_info['cams_info'])}")

        # 2D可视化 - 投影到图像
        if save_images:
            print("\n1. Projecting 3D boxes to camera images...")
            image_save_dir = os.path.join(save_dir, "images", sample_info['token'])
            self.visualize_boxes_on_images(sample_info, image_save_dir)

        # 3D可视化 - 点云和框
        if show_3d:
            print(f"\n2. Visualizing point cloud with 3D boxes (color mode: {color_mode}, colormap: {colormap})...")
            self.visualize_pointcloud_with_boxes(sample_info, color_mode=color_mode, colormap=colormap)

        print("\nDebug visualization completed!")

        return sample_info


# 使用示例
if __name__ == "__main__":
    # 创建转换器实例
    converter = Wenyuan2HV(
        root_path=root_path,
        label_list=label_list,
        with_camera=True,
        with_lidar=True
    )

    # 可视化示例 - 使用不同的颜色映射
    print("可用的颜色映射方案:")
    print("- 'jet': 蓝->青->绿->黄->红 (经典热力图)")
    print("- 'hot': 黑->红->黄->白 (热力图)")
    print("- 'cool': 青->紫 (冷色调)")
    print("- 'viridis': 紫->蓝->绿->黄 (感知均匀)")

    # 使用不同颜色映射可视化同一个样本
    sample_index = 0

    # 1. 使用jet颜色映射 (默认)
    print(f"\n=== 使用 jet 颜色映射可视化样本 {sample_index} ===")
    converter.debug_visualize_sample(
        sample_index=sample_index,
        save_images=False,
        show_3d=True,
        color_mode='intensity',
        colormap='jet'
    )

    # 2. 使用hot颜色映射
    print(f"\n=== 使用 hot 颜色映射可视化样本 {sample_index} ===")
    converter.debug_visualize_sample(
        sample_index=sample_index,
        save_images=False,
        show_3d=True,
        color_mode='intensity',
        colormap='hot'
    )

    # 3. 使用viridis颜色映射
    print(f"\n=== 使用 viridis 颜色映射可视化样本 {sample_index} ===")
    converter.debug_visualize_sample(
        sample_index=sample_index,
        save_images=False,
        show_3d=True,
        color_mode='intensity',
        colormap='viridis'
    )

    # 4. 对比：使用高度着色
    print(f"\n=== 使用高度着色可视化样本 {sample_index} ===")
    converter.debug_visualize_sample(
        sample_index=sample_index,
        save_images=False,
        show_3d=True,
        color_mode='height'
    )