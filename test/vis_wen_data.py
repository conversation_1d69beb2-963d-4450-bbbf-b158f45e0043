import os
import sys
import argparse
import open3d as o3d

from tools.data_converter.wenyuan_converter import Wenyuan2HV, label_list

test_data_path = 'data/we_data_single_clip'

converter = Wenyuan2HV(
    root_path=test_data_path,
    label_list=label_list,
    with_camera=True,
    with_lidar=True,
    max_sweeps=1
)

train_infos, val_infos = converter.create()

sample = converter.samples[10]

converter.debug_visualize_sample(
    sample_index=1,
    save_images=True,
    show_3d=True,
    color_mode='intensity',
)